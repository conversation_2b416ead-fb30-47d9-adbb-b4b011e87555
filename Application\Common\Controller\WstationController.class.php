<?php

namespace Common\Controller;

use Think\Controller;

/**
 * 商户后台管理控制
 */
class WstationController extends BaseController
{
    public $userRow = [];
    public $mod = 1; // 1 直接授权， 2 代理授权
    public $conf = [];

    /**
     * 初始化方法
     */
    public function _initialize()
    {
        $this->setConf([
            'number' => '2',
            'WX_ID' => 'wxbe5ef2f3c4081630',
            'WX_TOKEN' => 'zcgk123567',
            'WX_APPID' => 'wxbe5ef2f3c4081630',
            'WX_APPSECRET' => 'd31659831cf47f6fab621e7141ddb61a',
        ]);

        if (C('LOCAL_DEBUG')) {
            $user = D('User')->find(68);
            session('openid2', $user['openid']);
            session('wx.user_id2', $user['id']);
            // 在调试模式下也初始化微信分享隐藏功能
            $this->initWechatShareHide();
            return;
        }
        if (!isWX()) {
            header("Content-type: text/html; charset=utf-8");
            die('请通过微信使用此功能 :)');
        }

        // 初始化微信分享隐藏功能
        $this->initWechatShareHide();
    }

    /**
     * 检查招就办禁用状态
     * @param int $serviceStationId 服务站ID
     * @return bool true=已禁用，false=正常
     */
    protected function checkZsbDisabled($serviceStationId)
    {
        if (!$serviceStationId) {
            return false;
        }

        $serviceStation = D("ServiceStation")->where([
            'id' => $serviceStationId,
            'zsb_type' => 2
        ])->field('is_disabled')->find();

        return $serviceStation ? (bool)$serviceStation['is_disabled'] : false;
    }


    public function setConf($conf)
    {
        $this->conf = $conf;
    }

    /**
     * 微信code方式授权，获取 openid , scope:snsapi_base
     */
    public function codeAuth($conf = [])
    {
        if (!$conf) {
            $conf = $this->conf;
        }
        $suf = isset($conf['number']) ? $conf['number'] : '';
        if (!$this->authed($suf)) {
            vendor('LaneWeChat.lanewechat');
            if (!$code = I("get.code")) {
                \LaneWeChat\Core\Base::init($conf);
                $redirect_uri = preg_replace('/code=\w+\&?/', '', __HOST__ . $_SERVER['REQUEST_URI']);
                $redirect_uri = preg_replace('/\&?state=\w+\&?/', '', $redirect_uri);
                $state = (int)$conf['number'] > 10 ? $conf['number'] : (int)$conf['number'] + 1;
                if ($this->mod == 2) {
                    $state = base64_encode($redirect_uri);
                    $c_dm = D('Conf')->C('SYS_OUTER_DM_AUTH');
                    \LaneWeChat\Core\WeChatOAuth::getCode(proxy_uri($c_dm), $state);
                } else if ($this->mod == 1) {
                    \LaneWeChat\Core\WeChatOAuth::getCode($redirect_uri, $state);
                }
            } else {
                $row = [];
                \LaneWeChat\Core\Base::init($conf);
                unset($_GET['code']);
                $res = \LaneWeChat\Core\WeChatOAuth::getAccessTokenAndOpenId($code);
                if ($res['errcode']) {
                    dolog('error/wx/authcode', "code:$code, result:" . json_encode($res) . ' ' . (int)$conf['number'] . $_SERVER['HTTP_USER_AGENT']);
                    die('Failed to get openid, errcode:' . $res['errcode']); // 授权失败,记录日志
                } else {
                    $num = $conf['number'] > 10 ? 9 : $conf['number'];
                    if ($num == 9) {
                        $str = 'openid' . $num;
                        session($str, $res['openid']);
                    } else {
                        $str = 'openid2';
                        session($str, $res['openid']);
                    }
                }
            }
        }
        return true;
    }

    /**
     * 微信code方式授权，scope:snsapi_userinfo
     */
    public function codeAuth2()
    {
        if (!$this->loggedIn()) {
            vendor('LaneWeChat.lanewechat');
            $conf = $this->conf;
            $url = __HOST__ . $_SERVER['REQUEST_URI'];
            $code = $_REQUEST['code'];

            if (!empty($_REQUEST['code']) && cookie('curPath')) {
                unset($_REQUEST['code']);
                $url = cookie('curPath');
                session('auth2code', $code);
                $code = false;
                cookie('curPath', null);
            }
            \LaneWeChat\Core\Base::init($conf);
            if (!$code) {
                if ($this->mod == 2) {
                    $state = base64_encode($url);
                    \LaneWeChat\Core\WeChatOAuth::getCode(proxy_uri(D('Conf')->C('SYS_OUTER_DM_AUTH')), $state, 'snsapi_userinfo');
                } else if ($this->mod == 1) {
                    \LaneWeChat\Core\WeChatOAuth::getCode($url, '1', 'snsapi_userinfo');
                }
                exit;
            } else {
                unset($_GET['code']);
                //dolog('wx/authcode', "referer:{$_SERVER["HTTP_REFERER"]}, return data:".json_encode(I("get.")));
                $res = \LaneWeChat\Core\WeChatOAuth::getAccessTokenAndOpenId($code);

                if ($res['errcode']) {
                    dolog('error/wx/authcode3', "result:" . json_encode($res));
                } else {
                    session('openid', $res['openid']);
                    D("User")->init($res['openid'], $res['access_token'], 2); // 初始化用户表
                }
            }
            noCodeJump();

        }
    }

    /**
     * 微信端授权登录
     * @return bool
     */
    public function login($auth = true)
    {
        if ($this->loggedIn()) return true;
        if (!I("get.code")) {
            cookie('curPath', __HOST__ . $_SERVER['REQUEST_URI'], 60);
        }
        $res = $this->codeAuth();
        if ($res) {
            $user = D("User")->where(['openid' => session('openid')])->find();
            // 目前只需授权一次，获取用户信息
            if ($user && !empty($user['nickname'])) {
                session('wx.user_id', $user['id']);
                return true;
            } else {
                if ($auth === true) $this->codeAuth2();
            }
        }
    }

    /**
     * 是否已授权
     */
    public function authed()
    {
        return session('openid2') ? true : false;
    }

    /**
     * 是否已登录
     */
    public function loggedIn()
    {
        return session('wx.user_id2') ? true : false;
    }

    /**
     * 登出，清除session
     */
    public function logout()
    {
        session(null);
    }

    /**
     * 初始化微信分享隐藏功能
     * 自动隐藏微信浏览器中的分享菜单选项
     */
    protected function initWechatShareHide()
    {
        // 只在微信环境中执行
        if (!isWX() && !C('LOCAL_DEBUG')) {
            return;
        }

        try {
            // 获取当前页面URL
            $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
            $currentUrl = $protocol . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
            $currentUrl = preg_replace('/#.*$/', '', $currentUrl); // 移除锚点

            // 获取微信JSSDK配置
            $wxconf = null;
            if (!C('LOCAL_DEBUG')) {
                $wxconf = getWxConfig($currentUrl, $this->conf);
                // 记录配置信息用于调试
                error_log('微信配置生成: ' . json_encode($wxconf));
                error_log('当前URL: ' . $currentUrl);
                error_log('微信配置参数: ' . json_encode($this->conf));
            }

            // 传递配置到模板
            $this->assign('wechat_share_hide_enabled', true);
            $this->assign('wechat_share_hide_appid', $this->conf['WX_APPID']);

            if ($wxconf) {
                $this->assign('wechat_share_hide_config', $wxconf);
            } else {
                // 调试模式下的默认配置
                $this->assign('wechat_share_hide_config', [
                    'timestamp' => time(),
                    'noncestr' => 'debug_nonce_' . uniqid(),
                    'signature' => 'debug_signature'
                ]);
            }

            // 生成隐藏分享菜单的JavaScript代码
            $hideShareScript = $this->generateHideShareScript();
            $this->assign('wechat_share_hide_script', $hideShareScript);

        } catch (Exception $e) {
            // 静默处理错误，不影响正常页面显示
            error_log('微信分享隐藏功能初始化失败: ' . $e->getMessage());
        }
    }

    /**
     * 生成隐藏微信分享菜单的JavaScript代码
     * @return string
     */
    protected function generateHideShareScript()
    {
        // 获取配置数据
        $config = $this->get('wechat_share_hide_config');
        $appId = $this->conf['WX_APPID'];
        $timestamp = isset($config['timestamp']) ? $config['timestamp'] : time();
        $nonceStr = isset($config['noncestr']) ? $config['noncestr'] : 'default_nonce';
        $signature = isset($config['signature']) ? $config['signature'] : 'default_signature';

        return '
<script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
<script>
// 微信分享菜单隐藏功能
(function() {
    console.log("开始执行微信分享隐藏功能");

    // 检测是否在微信环境中
    function isWechatBrowser() {
        var ua = navigator.userAgent.toLowerCase();
        var isWechat = ua.indexOf("micromessenger") !== -1;
        console.log("用户代理:", ua);
        console.log("是否微信环境:", isWechat);
        return isWechat;
    }

    // 只在微信环境中执行
    if (!isWechatBrowser()) {
        console.log("非微信环境，跳过执行");
        return;
    }

    console.log("微信环境检测通过，开始配置JSSDK");

    // 等待wx对象加载
    function waitForWx() {
        if (typeof wx !== "undefined") {
            console.log("wx对象已加载，开始配置");

            console.log("配置参数:", {
                appId: "' . $appId . '",
                timestamp: ' . $timestamp . ',
                nonceStr: "' . $nonceStr . '",
                signature: "' . $signature . '"
            });

            wx.config({
                debug: true, // 开启调试模式
                appId: "' . $appId . '",
                timestamp: ' . $timestamp . ',
                nonceStr: "' . $nonceStr . '",
                signature: "' . $signature . '",
                jsApiList: [
                    "hideMenuItems",
                    "hideOptionMenu"
                ]
            });

            wx.ready(function() {
                console.log("微信JSSDK配置成功，开始隐藏菜单");

                // 先尝试隐藏整个选项菜单
                wx.hideOptionMenu();

                // 然后隐藏分享相关的菜单项
                wx.hideMenuItems({
                    menuList: [
                        "menuItem:share:appMessage",    // 分享给朋友
                        "menuItem:share:timeline",      // 分享到朋友圈
                        "menuItem:share:qq",            // 分享到QQ
                        "menuItem:share:weiboApp",      // 分享到微博
                        "menuItem:share:facebook",      // 分享到Facebook
                        "menuItem:share:QZone",         // 分享到QQ空间
                        "menuItem:copyUrl",             // 复制链接
                        "menuItem:openWithQQBrowser",   // 在QQ浏览器中打开
                        "menuItem:openWithSafari",      // 在Safari中打开
                        "menuItem:share:email",         // 邮件
                        "menuItem:share:brand"          // 一些其他分享选项
                    ],
                    success: function() {
                        console.log("微信分享菜单隐藏成功");
                    },
                    fail: function(err) {
                        console.log("隐藏微信分享菜单失败:", err);
                    }
                });
            });

            wx.error(function(res) {
                console.log("微信JSSDK配置失败:", res);
            });
        } else {
            console.log("wx对象未加载，1秒后重试");
            setTimeout(waitForWx, 1000);
        }
    }

    // 页面加载完成后执行
    if (document.readyState === "complete") {
        waitForWx();
    } else {
        window.addEventListener("load", waitForWx);
    }
})();
</script>';
    }

    /**
     * 重写display方法，自动注入微信分享隐藏功能
     */
    protected function display($templateFile='', $charset='', $contentType='', $content='', $prefix='') {
        // 如果启用了微信分享隐藏功能，在输出前注入JavaScript代码
        if ($this->get('wechat_share_hide_enabled')) {
            // 开启输出缓冲
            ob_start();

            // 调用父类的display方法
            parent::display($templateFile, $charset, $contentType, $content, $prefix);

            // 获取输出内容
            $output = ob_get_contents();
            ob_end_clean();

            // 在</head>标签前注入微信分享隐藏脚本
            $script = $this->get('wechat_share_hide_script');
            if ($script && strpos($output, '</head>') !== false) {
                $output = str_replace('</head>', $script . "\n</head>", $output);
            }

            // 输出修改后的内容
            echo $output;
        } else {
            // 直接调用父类的display方法
            parent::display($templateFile, $charset, $contentType, $content, $prefix);
        }
    }
}
