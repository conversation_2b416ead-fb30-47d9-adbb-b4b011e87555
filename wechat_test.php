<?php
// 微信JSSDK测试页面
require_once './Application/Common/Common/function.php';

// 微信配置
$conf = [
    'number' => '2',
    'WX_ID' => 'zhongcaiguoke',
    'WX_TOKEN' => 'zcgk123567',
    'WX_APPID' => 'wx6fddd8b81169ea8c',
    'WX_APPSECRET' => 'e9107e262a4b7ab677acd3ab0f072fca',
];

// 获取当前页面URL
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
$currentUrl = $protocol . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
$currentUrl = preg_replace('/#.*$/', '', $currentUrl);

// 获取微信配置
$wxconf = getWxConfig($currentUrl, $conf);

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信JSSDK测试</title>
</head>
<body>
    <h1>微信JSSDK测试页面</h1>
    <p>当前URL: <?php echo $currentUrl; ?></p>
    <p>微信配置:</p>
    <pre><?php echo json_encode($wxconf, JSON_PRETTY_PRINT); ?></pre>
    
    <div id="status">正在检测微信环境...</div>
    <div id="config-status">正在配置JSSDK...</div>
    <div id="hide-status">等待隐藏分享菜单...</div>

    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <script>
        // 检测微信环境
        var isWechat = /MicroMessenger/i.test(navigator.userAgent);
        document.getElementById('status').innerHTML = isWechat ? '✅ 在微信环境中' : '❌ 不在微信环境中';
        
        if (!isWechat) {
            document.getElementById('config-status').innerHTML = '❌ 跳过JSSDK配置（非微信环境）';
            document.getElementById('hide-status').innerHTML = '❌ 跳过分享菜单隐藏（非微信环境）';
        } else {
            // 配置微信JSSDK
            wx.config({
                debug: true, // 开启调试模式
                appId: '<?php echo $conf['WX_APPID']; ?>',
                timestamp: <?php echo $wxconf['timestamp']; ?>,
                nonceStr: '<?php echo $wxconf['noncestr']; ?>',
                signature: '<?php echo $wxconf['signature']; ?>',
                jsApiList: [
                    'hideMenuItems',
                    'hideOptionMenu'
                ]
            });
            
            wx.ready(function() {
                document.getElementById('config-status').innerHTML = '✅ JSSDK配置成功';
                
                // 隐藏分享菜单
                wx.hideOptionMenu();
                wx.hideMenuItems({
                    menuList: [
                        "menuItem:share:appMessage",
                        "menuItem:share:timeline",
                        "menuItem:share:qq",
                        "menuItem:share:weiboApp",
                        "menuItem:share:facebook",
                        "menuItem:share:QZone",
                        "menuItem:copyUrl"
                    ],
                    success: function() {
                        document.getElementById('hide-status').innerHTML = '✅ 分享菜单隐藏成功';
                    },
                    fail: function(err) {
                        document.getElementById('hide-status').innerHTML = '❌ 分享菜单隐藏失败: ' + JSON.stringify(err);
                    }
                });
            });
            
            wx.error(function(res) {
                document.getElementById('config-status').innerHTML = '❌ JSSDK配置失败: ' + JSON.stringify(res);
                document.getElementById('hide-status').innerHTML = '❌ 无法隐藏分享菜单（JSSDK配置失败）';
            });
        }
    </script>
</body>
</html>
